using Microsoft.Data.SqlClient;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Add CORS for local development
builder.Services.AddCors(options =>
{
    options.AddPolicy("LocalDevelopment", policy =>
    {
        policy.WithOrigins("http://localhost:3000", "http://********:5001")
              .AllowAnyMethod()
              .AllowAnyHeader()
              .AllowCredentials();
    });
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseCors("LocalDevelopment");
app.UseAuthorization();
app.MapControllers();

// Test database connection on startup
var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
try
{
    using var connection = new SqlConnection(connectionString);
    connection.Open();
    Console.WriteLine($"✅ Database connection successful: {connection.Database}");
    Console.WriteLine($"✅ Current user: {connection.DataSource}");
    connection.Close();
}
catch (Exception ex)
{
    Console.WriteLine($"❌ Database connection failed: {ex.Message}");
    Console.WriteLine($"Connection string: {connectionString?.Replace("Password=", "Password=***")}");
}

Console.WriteLine("🚀 GP Mobile API started successfully!");
Console.WriteLine("📊 Swagger UI: https://localhost:7001/swagger");
Console.WriteLine("🔍 Health check: https://localhost:7001/api/health");
Console.WriteLine("💾 Database test: https://localhost:7001/api/health/database");

app.Run();
