using GP.Mobile.Core.Repositories;
using GP.Mobile.Models.DTOs;
using Microsoft.Extensions.Logging;
using System.Data;

namespace GP.Mobile.Core.Services;

/// <summary>
/// Service for SoChungTu (Document Numbering) operations
/// Implements ALL methods from clsSoChungTu.cs (366 lines)
/// Manual implementation - Maintains 100% Legacy Compatibility
/// CRITICAL SYSTEM COMPONENT - Essential for document numbering across all transactions
/// </summary>
public interface ISoChungTuService
{
    #region Legacy Methods
    
    Task<bool> LoadAsync(string loaiChungTu);
    Task<bool> UndoVoucherAsync(string namThang, string loaiChungTu, double soChungTu);
    Task<string> TinhHauToAsync(string loai, string namThang);
    Task<string> CreateVoucherAsync(string loaiChungTu, string namThang);
    Task<DataTable> LoadChungTuAsync(string loai, string namThang, string condition = "");
    Task<string> CreateVoucherBGAsync(string loaiChungTu, string namThang, string coVan, string khoaCoVan);
    Task<string> CreateVoucherBaoGiaThucHienAsync(string loaiChungTu, string namThang, string maDaiLy);
    
    #endregion

    #region Modern API Methods
    
    Task<IEnumerable<SoChungTuListDto>> GetAllAsync();
    Task<SoChungTuDto?> GetByIdAsync(string loaiChungTu);
    Task<string> CreateAsync(CreateSoChungTuDto createDto);
    Task<bool> UpdateAsync(string loaiChungTu, UpdateSoChungTuDto updateDto);
    Task<bool> DeleteAsync(string loaiChungTu);
    Task<IEnumerable<SoChungTuListDto>> SearchAsync(SoChungTuSearchDto searchDto);
    Task<IEnumerable<SoChungTuLookupDto>> GetLookupAsync();
    Task<SoChungTuValidationDto> ValidateAsync(string loaiChungTu);
    Task<CreateVoucherResponseDto> GenerateDocumentNumberAsync(CreateVoucherRequestDto request);
    Task<bool> UndoDocumentNumberAsync(UndoVoucherRequestDto request);
    Task<IEnumerable<AutomotiveDocumentNumberingDto>> GetAutomotiveDocumentTypesAsync();
    Task<DocumentNumberingStatsDto> GetStatsAsync(string loaiChungTu);
    
    #endregion
}

/// <summary>
/// Implementation of SoChungTu service
/// Follows exact legacy business logic from clsSoChungTu.cs
/// </summary>
public class SoChungTuService : ISoChungTuService
{
    private readonly ISoChungTuRepository _repository;
    private readonly ILogger<SoChungTuService> _logger;

    public SoChungTuService(ISoChungTuRepository repository, ILogger<SoChungTuService> logger)
    {
        _repository = repository;
        _logger = logger;
    }

    #region Legacy Methods Implementation

    public async Task<bool> LoadAsync(string loaiChungTu)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(loaiChungTu))
            {
                throw new ArgumentException("LoaiChungTu không được để trống");
            }

            return await _repository.LoadAsync(loaiChungTu);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading SoChungTu");
            throw;
        }
    }

    public async Task<bool> UndoVoucherAsync(string namThang, string loaiChungTu, double soChungTu)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(namThang) || string.IsNullOrWhiteSpace(loaiChungTu))
            {
                throw new ArgumentException("NamThang và LoaiChungTu không được để trống");
            }

            return await _repository.UndoVoucherAsync(namThang, loaiChungTu, soChungTu);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error undoing voucher");
            throw;
        }
    }

    public async Task<string> TinhHauToAsync(string loai, string namThang)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(loai) || string.IsNullOrWhiteSpace(namThang))
            {
                throw new ArgumentException("Loai và NamThang không được để trống");
            }

            return await _repository.TinhHauToAsync(loai, namThang);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating suffix");
            throw;
        }
    }

    public async Task<string> CreateVoucherAsync(string loaiChungTu, string namThang)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(loaiChungTu) || string.IsNullOrWhiteSpace(namThang))
            {
                throw new ArgumentException("LoaiChungTu và NamThang không được để trống");
            }

            // Load document type configuration first
            await _repository.LoadAsync(loaiChungTu);
            
            return await _repository.CreateVoucherAsync(loaiChungTu, namThang);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating voucher");
            throw;
        }
    }

    public async Task<DataTable> LoadChungTuAsync(string loai, string namThang, string condition = "")
    {
        try
        {
            if (string.IsNullOrWhiteSpace(loai) || string.IsNullOrWhiteSpace(namThang))
            {
                throw new ArgumentException("Loai và NamThang không được để trống");
            }

            return await _repository.LoadChungTuAsync(loai, namThang, condition);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading document list");
            return new DataTable();
        }
    }

    public async Task<string> CreateVoucherBGAsync(string loaiChungTu, string namThang, string coVan, string khoaCoVan)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(loaiChungTu) || string.IsNullOrWhiteSpace(namThang))
            {
                throw new ArgumentException("LoaiChungTu và NamThang không được để trống");
            }

            // Load document type configuration first
            await _repository.LoadAsync(loaiChungTu);
            
            return await _repository.CreateVoucherBGAsync(loaiChungTu, namThang, coVan, khoaCoVan);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating BG voucher");
            throw;
        }
    }

    public async Task<string> CreateVoucherBaoGiaThucHienAsync(string loaiChungTu, string namThang, string maDaiLy)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(loaiChungTu) || string.IsNullOrWhiteSpace(namThang))
            {
                throw new ArgumentException("LoaiChungTu và NamThang không được để trống");
            }

            // Load document type configuration first
            await _repository.LoadAsync(loaiChungTu);
            
            return await _repository.CreateVoucherBaoGiaThucHienAsync(loaiChungTu, namThang, maDaiLy);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating BaoGia execution voucher");
            throw;
        }
    }

    #endregion

    #region Modern API Methods Implementation

    public async Task<IEnumerable<SoChungTuListDto>> GetAllAsync()
    {
        try
        {
            return await _repository.GetAllAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all document types");
            return new List<SoChungTuListDto>();
        }
    }

    public async Task<SoChungTuDto?> GetByIdAsync(string loaiChungTu)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(loaiChungTu))
                return null;

            return await _repository.GetByIdAsync(loaiChungTu);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting document type by ID");
            return null;
        }
    }

    public async Task<string> CreateAsync(CreateSoChungTuDto createDto)
    {
        try
        {
            // Validate input
            var validationResult = await ValidateForCreateAsync(createDto);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException(string.Join(", ", validationResult.Errors));
            }

            var result = await _repository.CreateAsync(createDto);
            return result.LoaiChungTu;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating document type");
            throw;
        }
    }

    public async Task<bool> UpdateAsync(string loaiChungTu, UpdateSoChungTuDto updateDto)
    {
        try
        {
            // Validate input
            var validationResult = await ValidateForUpdateAsync(loaiChungTu, updateDto);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException(string.Join(", ", validationResult.Errors));
            }

            var result = await _repository.UpdateAsync(loaiChungTu, updateDto);
            return result != null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating document type");
            throw;
        }
    }

    public async Task<bool> DeleteAsync(string loaiChungTu)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(loaiChungTu))
            {
                throw new ArgumentException("LoaiChungTu không được để trống");
            }

            // Check if can delete
            var canDelete = await CanDeleteAsync(loaiChungTu);
            if (!canDelete)
            {
                throw new InvalidOperationException("Không thể xóa loại chứng từ này vì đang được sử dụng");
            }

            return await _repository.DeleteAsync(loaiChungTu);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting document type");
            throw;
        }
    }

    public async Task<CreateVoucherResponseDto> GenerateDocumentNumberAsync(CreateVoucherRequestDto request)
    {
        try
        {
            // Validate request
            if (string.IsNullOrWhiteSpace(request.LoaiChungTu) || string.IsNullOrWhiteSpace(request.NamThang))
            {
                return new CreateVoucherResponseDto
                {
                    Success = false,
                    ErrorMessage = "LoaiChungTu và NamThang không được để trống"
                };
            }

            return await _repository.GenerateDocumentNumberAsync(request);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating document number");
            return new CreateVoucherResponseDto
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    public async Task<bool> UndoDocumentNumberAsync(UndoVoucherRequestDto request)
    {
        try
        {
            // Validate request
            if (string.IsNullOrWhiteSpace(request.LoaiChungTu) || string.IsNullOrWhiteSpace(request.NamThang))
            {
                throw new ArgumentException("LoaiChungTu và NamThang không được để trống");
            }

            return await _repository.UndoDocumentNumberAsync(request);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error undoing document number");
            throw;
        }
    }

    // Placeholder implementations for remaining modern API methods
    public async Task<IEnumerable<SoChungTuListDto>> SearchAsync(SoChungTuSearchDto searchDto) => new List<SoChungTuListDto>();
    public async Task<IEnumerable<SoChungTuLookupDto>> GetLookupAsync() => new List<SoChungTuLookupDto>();
    public async Task<SoChungTuValidationDto> ValidateAsync(string loaiChungTu) => new SoChungTuValidationDto();
    public async Task<IEnumerable<AutomotiveDocumentNumberingDto>> GetAutomotiveDocumentTypesAsync() => new List<AutomotiveDocumentNumberingDto>();
    public async Task<DocumentNumberingStatsDto> GetStatsAsync(string loaiChungTu) => new DocumentNumberingStatsDto();

    #endregion

    #region Private Helper Methods

    private async Task<ValidationResult> ValidateForCreateAsync(CreateSoChungTuDto dto)
    {
        var result = new ValidationResult();

        if (string.IsNullOrWhiteSpace(dto.LoaiChungTu))
            result.Errors.Add("LoaiChungTu không được để trống");

        if (string.IsNullOrWhiteSpace(dto.Loai))
            result.Errors.Add("Loai không được để trống");

        if (dto.ChieuDai <= 0 || dto.ChieuDai > 20)
            result.Errors.Add("ChieuDai phải từ 1 đến 20");

        // Check for duplicate
        var existing = await _repository.GetByIdAsync(dto.LoaiChungTu);
        if (existing != null)
            result.Errors.Add("LoaiChungTu đã tồn tại");

        result.IsValid = !result.Errors.Any();
        return result;
    }

    private async Task<ValidationResult> ValidateForUpdateAsync(string loaiChungTu, UpdateSoChungTuDto dto)
    {
        var result = new ValidationResult();

        if (string.IsNullOrWhiteSpace(loaiChungTu))
            result.Errors.Add("LoaiChungTu không được để trống");

        if (dto.ChieuDai <= 0 || dto.ChieuDai > 20)
            result.Errors.Add("ChieuDai phải từ 1 đến 20");

        // Check if record exists
        var existing = await _repository.GetByIdAsync(loaiChungTu);
        if (existing == null)
            result.Errors.Add("Không tìm thấy loại chứng từ");

        result.IsValid = !result.Errors.Any();
        return result;
    }

    private async Task<bool> CanDeleteAsync(string loaiChungTu)
    {
        // Check if this document type is being used
        // This would involve checking related tables
        // For now, return true - implement actual business logic as needed
        await Task.CompletedTask;
        return true;
    }

    #endregion
}

/// <summary>
/// Validation result helper class
/// </summary>
public class ValidationResult
{
    public bool IsValid { get; set; } = true;
    public List<string> Errors { get; set; } = new List<string>();
}
