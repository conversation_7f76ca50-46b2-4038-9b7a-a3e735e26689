using System.Data;
using Dapper;
using GP.Mobile.Core.Interfaces;
using GP.Mobile.Models.DTOs;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace GP.Mobile.Data.Repositories
{
    /// <summary>
    /// Repository implementation for Insurance Image Management (Báo Gi<PERSON> Ảnh <PERSON>)
    /// Implements exact legacy functionality from clsBaoGiaHinhAnhBH.cs
    /// Handles BLOB data operations for insurance approval images
    /// </summary>
    public class BaoGiaHinhAnhBHRepository : IBaoGiaHinhAnhBHRepository
    {
        private readonly string _connectionString;
        private readonly ILogger<BaoGiaHinhAnhBHRepository> _logger;

        public BaoGiaHinhAnhBHRepository(IConfiguration configuration, ILogger<BaoGiaHinhAnhBHRepository> logger)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection") ?? throw new ArgumentNullException(nameof(configuration));
            _logger = logger;
        }

        /// <summary>
        /// Load insurance image by quotation ID
        /// Exact implementation from legacy Load method (lines 50-77)
        /// </summary>
        public async Task<BaoGiaHinhAnhBHDto?> LoadAsync(string khoaBaoGia)
        {
            try
            {
                const string sql = "SELECT * FROM [SC_BaoGiaHinhAnhDuyetGiaBH] WHERE KhoaBaoGia = @KhoaBaoGia";
                
                using var connection = new SqlConnection(_connectionString);
                var result = await connection.QueryFirstOrDefaultAsync<BaoGiaHinhAnhBHDto>(sql, new { KhoaBaoGia = khoaBaoGia });
                
                if (result != null)
                {
                    // Set computed properties
                    result.CoHinhAnh = result.HinhAnh != null && result.HinhAnh.Length > 0;
                    result.DaDuyet = result.TrangThaiDuyet == 1;
                    result.BiTuChoi = result.TrangThaiDuyet == 2;
                    result.DangChoDuyet = result.TrangThaiDuyet == 0;
                    
                    // Set display text
                    result.TrangThaiText = result.TrangThaiDuyet switch
                    {
                        0 => "Đang chờ duyệt",
                        1 => "Đã duyệt",
                        2 => "Bị từ chối",
                        _ => "Không xác định"
                    };

                    // Format file size
                    if (result.KichThuocFile > 0)
                    {
                        result.KichThuocFileText = FormatFileSize(result.KichThuocFile);
                    }

                    // Format date
                    if (!string.IsNullOrEmpty(result.NgayTao) && result.NgayTao.Length == 8)
                    {
                        result.NgayTaoFormatted = $"{result.NgayTao.Substring(6, 2)}/{result.NgayTao.Substring(4, 2)}/{result.NgayTao.Substring(0, 4)}";
                    }

                    // Generate Base64 for mobile display (thumbnail only for performance)
                    if (result.HinhAnhThumbnail != null && result.HinhAnhThumbnail.Length > 0)
                    {
                        result.ThumbnailBase64 = Convert.ToBase64String(result.HinhAnhThumbnail);
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading insurance image for quotation {KhoaBaoGia}", khoaBaoGia);
                return null;
            }
        }

        /// <summary>
        /// Save insurance image data using stored procedure
        /// Exact implementation from legacy Save method (lines 80-97)
        /// </summary>
        public async Task<bool> SaveAsync(BaoGiaHinhAnhBHDto insuranceImage)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                
                var parameters = new DynamicParameters();
                parameters.Add("@KhoaBaoGia", insuranceImage.KhoaBaoGia, DbType.String);
                parameters.Add("@HinhAnh", insuranceImage.HinhAnh ?? (object)DBNull.Value, DbType.Binary);
                parameters.Add("@NgayTao", insuranceImage.NgayTao, DbType.String);
                parameters.Add("@NguoiTao", insuranceImage.NguoiTao, DbType.String);
                parameters.Add("@MoTa", insuranceImage.MoTa, DbType.String);
                parameters.Add("@TenFile", insuranceImage.TenFile, DbType.String);
                parameters.Add("@KichThuocFile", insuranceImage.KichThuocFile, DbType.Int64);
                parameters.Add("@LoaiFile", insuranceImage.LoaiFile, DbType.String);
                parameters.Add("@TrangThaiDuyet", insuranceImage.TrangThaiDuyet, DbType.Int32);
                parameters.Add("@TenGiamDinh", insuranceImage.TenGiamDinh, DbType.String);
                parameters.Add("@KhoaBaoHiem", insuranceImage.KhoaBaoHiem, DbType.String);
                parameters.Add("@NhanXetGiamDinh", insuranceImage.NhanXetGiamDinh, DbType.String);
                parameters.Add("@SoTienDuyet", insuranceImage.SoTienDuyet, DbType.Decimal);
                parameters.Add("@ViTriChup", insuranceImage.ViTriChup, DbType.String);
                parameters.Add("@ThoiGianChup", insuranceImage.ThoiGianChup, DbType.String);
                parameters.Add("@ThietBiChup", insuranceImage.ThietBiChup, DbType.String);
                parameters.Add("@HinhAnhThumbnail", insuranceImage.HinhAnhThumbnail ?? (object)DBNull.Value, DbType.Binary);

                await connection.ExecuteAsync("sp_SC_BaoGiaHinhAnhDuyetGiaBH", parameters, commandType: CommandType.StoredProcedure);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving insurance image for quotation {KhoaBaoGia}", insuranceImage.KhoaBaoGia);
                return false;
            }
        }

        /// <summary>
        /// Delete insurance image by quotation ID
        /// </summary>
        public async Task<bool> DeleteAsync(string khoaBaoGia)
        {
            try
            {
                const string sql = "DELETE FROM [SC_BaoGiaHinhAnhDuyetGiaBH] WHERE KhoaBaoGia = @KhoaBaoGia";
                
                using var connection = new SqlConnection(_connectionString);
                var rowsAffected = await connection.ExecuteAsync(sql, new { KhoaBaoGia = khoaBaoGia });
                
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting insurance image for quotation {KhoaBaoGia}", khoaBaoGia);
                return false;
            }
        }

        /// <summary>
        /// Upload insurance image
        /// </summary>
        public async Task<bool> UploadImageAsync(UploadInsuranceImageDto uploadDto)
        {
            try
            {
                // Generate thumbnail
                var thumbnail = GenerateThumbnail(uploadDto.HinhAnh);
                
                var insuranceImage = new BaoGiaHinhAnhBHDto
                {
                    KhoaBaoGia = uploadDto.KhoaBaoGia,
                    HinhAnh = uploadDto.HinhAnh,
                    HinhAnhThumbnail = thumbnail,
                    NgayTao = DateTime.Now.ToString("yyyyMMdd"),
                    NguoiTao = uploadDto.NguoiTao,
                    MoTa = uploadDto.MoTa,
                    TenFile = uploadDto.TenFile,
                    KichThuocFile = uploadDto.HinhAnh.Length,
                    LoaiFile = uploadDto.LoaiFile,
                    TrangThaiDuyet = 0, // Pending approval
                    ViTriChup = uploadDto.ViTriChup,
                    ThoiGianChup = DateTime.Now.ToString("yyyyMMddHHmmss"),
                    ThietBiChup = uploadDto.ThietBiChup
                };

                return await SaveAsync(insuranceImage);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading insurance image for quotation {KhoaBaoGia}", uploadDto.KhoaBaoGia);
                return false;
            }
        }

        /// <summary>
        /// Update insurance approval status
        /// </summary>
        public async Task<bool> UpdateApprovalStatusAsync(InsuranceApprovalDto approvalDto)
        {
            try
            {
                const string sql = @"
                    UPDATE [SC_BaoGiaHinhAnhDuyetGiaBH] 
                    SET TrangThaiDuyet = @TrangThaiDuyet,
                        TenGiamDinh = @TenGiamDinh,
                        NhanXetGiamDinh = @NhanXetGiamDinh,
                        SoTienDuyet = @SoTienDuyet,
                        KhoaBaoHiem = @KhoaBaoHiem,
                        NgayDuyet = @NgayDuyet,
                        NguoiDuyet = @NguoiDuyet
                    WHERE KhoaBaoGia = @KhoaBaoGia";
                
                using var connection = new SqlConnection(_connectionString);
                var parameters = new
                {
                    KhoaBaoGia = approvalDto.KhoaBaoGia,
                    TrangThaiDuyet = approvalDto.TrangThaiDuyet,
                    TenGiamDinh = approvalDto.TenGiamDinh,
                    NhanXetGiamDinh = approvalDto.NhanXetGiamDinh,
                    SoTienDuyet = approvalDto.SoTienDuyet,
                    KhoaBaoHiem = approvalDto.KhoaBaoHiem,
                    NgayDuyet = DateTime.Now.ToString("yyyyMMdd"),
                    NguoiDuyet = approvalDto.NguoiDuyet
                };
                
                var rowsAffected = await connection.ExecuteAsync(sql, parameters);
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating approval status for quotation {KhoaBaoGia}", approvalDto.KhoaBaoGia);
                return false;
            }
        }

        /// <summary>
        /// Get insurance images requiring approval
        /// </summary>
        public async Task<List<InsuranceImageListDto>> GetPendingApprovalsAsync(string donViId)
        {
            try
            {
                const string sql = @"
                    SELECT 
                        img.KhoaBaoGia,
                        bg.SoChungTu,
                        bg.SoXe,
                        bg.TenKhachHang,
                        bh.TenViet as TenBaoHiem,
                        img.TrangThaiDuyet,
                        img.NgayTao,
                        img.TenGiamDinh,
                        img.SoTienDuyet,
                        CASE WHEN img.HinhAnh IS NOT NULL THEN 1 ELSE 0 END as CoHinhAnh,
                        'Đang chờ duyệt' as TrangThaiText
                    FROM [SC_BaoGiaHinhAnhDuyetGiaBH] img
                    INNER JOIN [SC_BaoGia] bg ON img.KhoaBaoGia = bg.Khoa
                    LEFT JOIN [DM_DoiTuong] bh ON bg.KhoaHangBaoHiem = bh.Khoa
                    WHERE img.TrangThaiDuyet = 0 
                    AND bg.KhoaDonVi = @DonViId
                    ORDER BY img.NgayTao DESC";
                
                using var connection = new SqlConnection(_connectionString);
                var result = await connection.QueryAsync<InsuranceImageListDto>(sql, new { DonViId = donViId });
                
                return result.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting pending approvals for unit {DonViId}", donViId);
                return new List<InsuranceImageListDto>();
            }
        }

        // Additional methods will be implemented in the next part...
        
        /// <summary>
        /// Helper method to format file size
        /// </summary>
        private static string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        /// <summary>
        /// Helper method to generate thumbnail
        /// </summary>
        private static byte[] GenerateThumbnail(byte[] imageData)
        {
            // Simplified thumbnail generation - in production, use proper image processing library
            // For now, return a smaller version or the original if small enough
            if (imageData.Length <= 50000) // 50KB
                return imageData;
            
            // In production, implement proper image resizing here
            // For now, return first 50KB as placeholder
            var thumbnail = new byte[Math.Min(50000, imageData.Length)];
            Array.Copy(imageData, thumbnail, thumbnail.Length);
            return thumbnail;
        }

        // Placeholder implementations for remaining interface methods
        public async Task<List<InsuranceImageListDto>> GetApprovedImagesAsync(string donViId, string fromDate, string toDate)
        {
            // Implementation will be added in next part
            return new List<InsuranceImageListDto>();
        }

        public async Task<List<InsuranceImageListDto>> GetRejectedImagesAsync(string donViId, string fromDate, string toDate)
        {
            // Implementation will be added in next part
            return new List<InsuranceImageListDto>();
        }

        public async Task<List<InsuranceImageListDto>> GetImagesByInsuranceCompanyAsync(string khoaBaoHiem, string fromDate, string toDate)
        {
            // Implementation will be added in next part
            return new List<InsuranceImageListDto>();
        }

        public async Task<InsuranceImageStatisticsDto> GetImageStatisticsAsync(string donViId, string fromDate, string toDate)
        {
            // Implementation will be added in next part
            return new InsuranceImageStatisticsDto();
        }

        public async Task<bool> HasInsuranceImageAsync(string khoaBaoGia)
        {
            try
            {
                const string sql = "SELECT COUNT(1) FROM [SC_BaoGiaHinhAnhDuyetGiaBH] WHERE KhoaBaoGia = @KhoaBaoGia AND HinhAnh IS NOT NULL";
                using var connection = new SqlConnection(_connectionString);
                var count = await connection.QuerySingleAsync<int>(sql, new { KhoaBaoGia = khoaBaoGia });
                return count > 0;
            }
            catch
            {
                return false;
            }
        }

        public async Task<byte[]?> GetImageThumbnailAsync(string khoaBaoGia)
        {
            try
            {
                const string sql = "SELECT HinhAnhThumbnail FROM [SC_BaoGiaHinhAnhDuyetGiaBH] WHERE KhoaBaoGia = @KhoaBaoGia";
                using var connection = new SqlConnection(_connectionString);
                return await connection.QuerySingleOrDefaultAsync<byte[]>(sql, new { KhoaBaoGia = khoaBaoGia });
            }
            catch
            {
                return null;
            }
        }

        public async Task<byte[]?> GetFullImageAsync(string khoaBaoGia)
        {
            try
            {
                const string sql = "SELECT HinhAnh FROM [SC_BaoGiaHinhAnhDuyetGiaBH] WHERE KhoaBaoGia = @KhoaBaoGia";
                using var connection = new SqlConnection(_connectionString);
                return await connection.QuerySingleOrDefaultAsync<byte[]>(sql, new { KhoaBaoGia = khoaBaoGia });
            }
            catch
            {
                return null;
            }
        }

        public async Task<bool> UpdateImageMetadataAsync(string khoaBaoGia, string moTa, string tenFile)
        {
            try
            {
                const string sql = "UPDATE [SC_BaoGiaHinhAnhDuyetGiaBH] SET MoTa = @MoTa, TenFile = @TenFile WHERE KhoaBaoGia = @KhoaBaoGia";
                using var connection = new SqlConnection(_connectionString);
                var rowsAffected = await connection.ExecuteAsync(sql, new { KhoaBaoGia = khoaBaoGia, MoTa = moTa, TenFile = tenFile });
                return rowsAffected > 0;
            }
            catch
            {
                return false;
            }
        }

        public async Task<List<InsuranceImageListDto>> GetImagesByStatusAsync(int trangThaiDuyet, string donViId)
        {
            // Implementation will be added in next part
            return new List<InsuranceImageListDto>();
        }

        public async Task<int> BulkApproveImagesAsync(List<string> khoaBaoGiaList, InsuranceApprovalDto approvalDto)
        {
            // Implementation will be added in next part
            return 0;
        }

        public async Task<PaginatedResult<InsuranceImageListDto>> GetImagesForMobileAsync(string donViId, int pageSize, int pageNumber)
        {
            // Implementation will be added in next part
            return new PaginatedResult<InsuranceImageListDto>();
        }
    }
}
