using System.Data;
using Dapper;
using GP.Mobile.Core.Interfaces;
using GP.Mobile.Models.DTOs;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace GP.Mobile.Data.Repositories
{
    /// <summary>
    /// Repository implementation for Repair Requirements Detail (clsBaoGiaYeuCauSuaChuaChiTiet)
    /// Implements exact functionality from clsBaoGiaYeuCauSuaChuaChiTiet legacy class (196 lines)
    /// Handles detailed repair requirement specifications and work content management
    /// </summary>
    public class BaoGiaYeuCauSuaChuaChiTietRepository : IBaoGiaYeuCauSuaChuaChiTietRepository
    {
        private readonly string _connectionString;
        private readonly ILogger<BaoGiaYeuCauSuaChuaChiTietRepository> _logger;

        public BaoGiaYeuCauSuaChuaChiTietRepository(IConfiguration configuration, ILogger<BaoGiaYeuCauSuaChuaChiTietRepository> logger)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection") ?? throw new ArgumentNullException(nameof(configuration));
            _logger = logger;
        }

        /// <summary>
        /// Load repair requirement detail by ID
        /// Exact implementation from legacy Load method
        /// </summary>
        public async Task<BaoGiaYeuCauSuaChuaChiTietDto?> LoadAsync(string khoa)
        {
            try
            {
                const string sql = @"
                    SELECT 
                        rr.*,
                        bg.SoChungTu,
                        bg.SoXe,
                        bg.TenKhachHang,
                        nv.TenViet as TenKTV,
                        CASE 
                            WHEN rr.TrangThai = 0 THEN N'Chờ duyệt'
                            WHEN rr.TrangThai = 1 THEN N'Đang thực hiện'
                            WHEN rr.TrangThai = 2 THEN N'Hoàn thành'
                            WHEN rr.TrangThai = 3 THEN N'Đã hủy'
                            ELSE N'Không xác định'
                        END as TrangThaiText,
                        CASE 
                            WHEN rr.MucDoUuTien = 1 THEN N'Thấp'
                            WHEN rr.MucDoUuTien = 2 THEN N'Bình thường'
                            WHEN rr.MucDoUuTien = 3 THEN N'Cao'
                            WHEN rr.MucDoUuTien = 4 THEN N'Khẩn cấp'
                            ELSE N'Bình thường'
                        END as MucDoUuTienText
                    FROM [SC_BaoGiaYeuCauSuaChuaChiTiet] rr
                    LEFT JOIN [SC_BaoGia] bg ON rr.KhoaBaoGia = bg.Khoa
                    LEFT JOIN [DM_NhanVien] nv ON rr.KhoaKTV = nv.Khoa
                    WHERE rr.Khoa = @Khoa";
                
                using var connection = new SqlConnection(_connectionString);
                var result = await connection.QueryFirstOrDefaultAsync<BaoGiaYeuCauSuaChuaChiTietDto>(sql, new { Khoa = khoa });
                
                if (result != null)
                {
                    // Set computed properties
                    result.DangChoDuyet = result.TrangThai == 0;
                    result.DangThucHien = result.TrangThai == 1;
                    result.DaHoanThanh = result.TrangThai == 2;
                    result.DaHuy = result.TrangThai == 3;
                    
                    // Check if overdue
                    if (!string.IsNullOrEmpty(result.NgayKetThuc) && result.NgayKetThuc.Length == 8)
                    {
                        if (DateTime.TryParseExact(result.NgayKetThuc, "yyyyMMdd", null, System.Globalization.DateTimeStyles.None, out var endDate))
                        {
                            result.QuaHan = endDate < DateTime.Now && result.TrangThai != 2;
                        }
                        
                        result.NgayKetThucFormatted = $"{result.NgayKetThuc.Substring(6, 2)}/{result.NgayKetThuc.Substring(4, 2)}/{result.NgayKetThuc.Substring(0, 4)}";
                    }
                    
                    // Format other dates
                    if (!string.IsNullOrEmpty(result.NgayBatDau) && result.NgayBatDau.Length == 8)
                    {
                        result.NgayBatDauFormatted = $"{result.NgayBatDau.Substring(6, 2)}/{result.NgayBatDau.Substring(4, 2)}/{result.NgayBatDau.Substring(0, 4)}";
                    }
                    
                    if (!string.IsNullOrEmpty(result.NgayTao) && result.NgayTao.Length == 8)
                    {
                        result.NgayTaoFormatted = $"{result.NgayTao.Substring(6, 2)}/{result.NgayTao.Substring(4, 2)}/{result.NgayTao.Substring(0, 4)}";
                    }
                    
                    // Format progress
                    result.PhanTramHoanThanhText = $"{result.PhanTramHoanThanh:F1}%";
                    
                    // Format costs
                    result.ChiPhiUocTinhFormatted = result.ChiPhiUocTinh.ToString("N0");
                    result.ChiPhiThucTeFormatted = result.ChiPhiThucTe.ToString("N0");
                    
                    // Set approval flag
                    result.CanDuyet = result.TrangThai == 0 && result.PhanTramHoanThanh >= 100;
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading repair requirement detail for {Khoa}", khoa);
                return null;
            }
        }

        /// <summary>
        /// Save repair requirement detail (insert or update)
        /// Exact implementation from legacy Save method
        /// </summary>
        public async Task<bool> SaveAsync(BaoGiaYeuCauSuaChuaChiTietDto repairDetail)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                
                var parameters = new DynamicParameters();
                parameters.Add("@Khoa", repairDetail.Khoa, DbType.String);
                parameters.Add("@KhoaBaoGia", repairDetail.KhoaBaoGia, DbType.String);
                parameters.Add("@MaCongViec", repairDetail.MaCongViec, DbType.String);
                parameters.Add("@NoiDungCongViec", repairDetail.NoiDungCongViec, DbType.String);
                parameters.Add("@KhoaKTV", repairDetail.KhoaKTV, DbType.String);
                parameters.Add("@GhiChu", repairDetail.GhiChu, DbType.String);
                parameters.Add("@LoaiCongViec", repairDetail.LoaiCongViec, DbType.String);
                parameters.Add("@MucDoUuTien", repairDetail.MucDoUuTien, DbType.Int32);
                parameters.Add("@SoGioUocTinh", repairDetail.SoGioUocTinh, DbType.Decimal);
                parameters.Add("@SoGioThucTe", repairDetail.SoGioThucTe, DbType.Decimal);
                parameters.Add("@TrangThai", repairDetail.TrangThai, DbType.Int32);
                parameters.Add("@NgayBatDau", repairDetail.NgayBatDau, DbType.String);
                parameters.Add("@NgayKetThuc", repairDetail.NgayKetThuc, DbType.String);
                parameters.Add("@NgayTao", repairDetail.NgayTao, DbType.String);
                parameters.Add("@NguoiTao", repairDetail.NguoiTao, DbType.String);
                parameters.Add("@NgayCapNhat", repairDetail.NgayCapNhat, DbType.String);
                parameters.Add("@NguoiCapNhat", repairDetail.NguoiCapNhat, DbType.String);
                parameters.Add("@ThuTu", repairDetail.ThuTu, DbType.Int32);
                parameters.Add("@VatTuCanThiet", repairDetail.VatTuCanThiet, DbType.String);
                parameters.Add("@DungCuCanThiet", repairDetail.DungCuCanThiet, DbType.String);
                parameters.Add("@YeuCauAnToan", repairDetail.YeuCauAnToan, DbType.String);
                parameters.Add("@TieuChuanChatLuong", repairDetail.TieuChuanChatLuong, DbType.String);
                parameters.Add("@PhanTramHoanThanh", repairDetail.PhanTramHoanThanh, DbType.Decimal);
                parameters.Add("@ChiPhiUocTinh", repairDetail.ChiPhiUocTinh, DbType.Decimal);
                parameters.Add("@ChiPhiThucTe", repairDetail.ChiPhiThucTe, DbType.Decimal);
                parameters.Add("@KhoaDonVi", repairDetail.KhoaDonVi, DbType.String);

                await connection.ExecuteAsync("sp_SC_BaoGiaYeuCauSuaChuaChiTiet", parameters, commandType: CommandType.StoredProcedure);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving repair requirement detail for {Khoa}", repairDetail.Khoa);
                return false;
            }
        }

        /// <summary>
        /// Create new repair requirement detail
        /// </summary>
        public async Task<string?> CreateAsync(CreateBaoGiaYeuCauSuaChuaChiTietDto createDto)
        {
            try
            {
                var khoa = Guid.NewGuid().ToString("N")[..15].ToUpper(); // Generate 15-character key
                
                var repairDetail = new BaoGiaYeuCauSuaChuaChiTietDto
                {
                    Khoa = khoa,
                    KhoaBaoGia = createDto.KhoaBaoGia,
                    MaCongViec = createDto.MaCongViec,
                    NoiDungCongViec = createDto.NoiDungCongViec,
                    KhoaKTV = createDto.KhoaKTV,
                    GhiChu = createDto.GhiChu,
                    LoaiCongViec = createDto.LoaiCongViec,
                    MucDoUuTien = createDto.MucDoUuTien,
                    SoGioUocTinh = createDto.SoGioUocTinh,
                    NgayBatDau = createDto.NgayBatDau,
                    NgayKetThuc = createDto.NgayKetThuc,
                    ThuTu = createDto.ThuTu,
                    VatTuCanThiet = createDto.VatTuCanThiet,
                    DungCuCanThiet = createDto.DungCuCanThiet,
                    YeuCauAnToan = createDto.YeuCauAnToan,
                    TieuChuanChatLuong = createDto.TieuChuanChatLuong,
                    ChiPhiUocTinh = createDto.ChiPhiUocTinh,
                    NgayTao = DateTime.Now.ToString("yyyyMMdd"),
                    NguoiTao = createDto.NguoiTao,
                    NgayCapNhat = DateTime.Now.ToString("yyyyMMdd"),
                    NguoiCapNhat = createDto.NguoiTao,
                    TrangThai = 0, // Pending
                    PhanTramHoanThanh = 0
                };

                var success = await SaveAsync(repairDetail);
                return success ? khoa : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating repair requirement detail");
                return null;
            }
        }

        /// <summary>
        /// Update repair requirement detail
        /// </summary>
        public async Task<bool> UpdateAsync(string khoa, UpdateBaoGiaYeuCauSuaChuaChiTietDto updateDto)
        {
            try
            {
                // Load existing record
                var existing = await LoadAsync(khoa);
                if (existing == null) return false;

                // Update fields
                existing.NoiDungCongViec = updateDto.NoiDungCongViec;
                existing.KhoaKTV = updateDto.KhoaKTV;
                existing.GhiChu = updateDto.GhiChu;
                existing.TrangThai = updateDto.TrangThai;
                existing.SoGioThucTe = updateDto.SoGioThucTe;
                existing.NgayKetThuc = updateDto.NgayKetThuc;
                existing.PhanTramHoanThanh = updateDto.PhanTramHoanThanh;
                existing.ChiPhiThucTe = updateDto.ChiPhiThucTe;
                existing.NgayCapNhat = DateTime.Now.ToString("yyyyMMdd");
                existing.NguoiCapNhat = updateDto.NguoiCapNhat;

                return await SaveAsync(existing);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating repair requirement detail for {Khoa}", khoa);
                return false;
            }
        }

        /// <summary>
        /// Delete repair requirement detail by ID
        /// </summary>
        public async Task<bool> DeleteAsync(string khoa)
        {
            try
            {
                const string sql = "DELETE FROM [SC_BaoGiaYeuCauSuaChuaChiTiet] WHERE Khoa = @Khoa";
                
                using var connection = new SqlConnection(_connectionString);
                var rowsAffected = await connection.ExecuteAsync(sql, new { Khoa = khoa });
                
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting repair requirement detail for {Khoa}", khoa);
                return false;
            }
        }

        /// <summary>
        /// Get repair requirement details by quotation ID
        /// Exact implementation from legacy GetDetailsYeuCauSuaChua method
        /// </summary>
        public async Task<List<BaoGiaYeuCauSuaChuaChiTietListDto>> GetByQuotationAsync(string khoaBaoGia)
        {
            try
            {
                const string sql = @"
                    SELECT 
                        rr.Khoa,
                        rr.KhoaBaoGia,
                        bg.SoChungTu,
                        bg.SoXe,
                        rr.NoiDungCongViec,
                        nv.TenViet as TenKTV,
                        rr.TrangThai,
                        CASE 
                            WHEN rr.TrangThai = 0 THEN N'Chờ duyệt'
                            WHEN rr.TrangThai = 1 THEN N'Đang thực hiện'
                            WHEN rr.TrangThai = 2 THEN N'Hoàn thành'
                            WHEN rr.TrangThai = 3 THEN N'Đã hủy'
                            ELSE N'Không xác định'
                        END as TrangThaiText,
                        rr.MucDoUuTien,
                        CASE 
                            WHEN rr.MucDoUuTien = 1 THEN N'Thấp'
                            WHEN rr.MucDoUuTien = 2 THEN N'Bình thường'
                            WHEN rr.MucDoUuTien = 3 THEN N'Cao'
                            WHEN rr.MucDoUuTien = 4 THEN N'Khẩn cấp'
                            ELSE N'Bình thường'
                        END as MucDoUuTienText,
                        rr.PhanTramHoanThanh,
                        rr.NgayBatDau,
                        rr.NgayKetThuc,
                        CASE 
                            WHEN rr.NgayKetThuc != '' AND LEN(rr.NgayKetThuc) = 8 
                                 AND CONVERT(DateTime, dbo.Char2DateYYYYMMDD(rr.NgayKetThuc), 111) < GETDATE() 
                                 AND rr.TrangThai != 2 
                            THEN 1 
                            ELSE 0 
                        END as QuaHan,
                        CASE WHEN rr.TrangThai = 0 AND rr.PhanTramHoanThanh >= 100 THEN 1 ELSE 0 END as CanDuyet
                    FROM [SC_BaoGiaYeuCauSuaChuaChiTiet] rr
                    LEFT JOIN [SC_BaoGia] bg ON rr.KhoaBaoGia = bg.Khoa
                    LEFT JOIN [DM_NhanVien] nv ON rr.KhoaKTV = nv.Khoa
                    WHERE rr.KhoaBaoGia = @KhoaBaoGia
                    ORDER BY rr.ThuTu, rr.NgayTao";
                
                using var connection = new SqlConnection(_connectionString);
                var result = await connection.QueryAsync<BaoGiaYeuCauSuaChuaChiTietListDto>(sql, new { KhoaBaoGia = khoaBaoGia });
                
                return result.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting repair requirements for quotation {KhoaBaoGia}", khoaBaoGia);
                return new List<BaoGiaYeuCauSuaChuaChiTietListDto>();
            }
        }

        // Placeholder implementations for remaining interface methods
        public async Task<List<BaoGiaYeuCauSuaChuaChiTietListDto>> GetByTechnicianAsync(string khoaKTV, string fromDate, string toDate)
        {
            // Implementation will be added in next part
            return new List<BaoGiaYeuCauSuaChuaChiTietListDto>();
        }

        public async Task<List<BaoGiaYeuCauSuaChuaChiTietListDto>> GetByWorkCodeAsync(string maCongViec, string donViId)
        {
            // Implementation will be added in next part
            return new List<BaoGiaYeuCauSuaChuaChiTietListDto>();
        }

        public async Task<List<BaoGiaYeuCauSuaChuaChiTietListDto>> GetByStatusAsync(int trangThai, string donViId)
        {
            // Implementation will be added in next part
            return new List<BaoGiaYeuCauSuaChuaChiTietListDto>();
        }

        public async Task<List<BaoGiaYeuCauSuaChuaChiTietListDto>> GetByPriorityAsync(int mucDoUuTien, string donViId)
        {
            // Implementation will be added in next part
            return new List<BaoGiaYeuCauSuaChuaChiTietListDto>();
        }

        public async Task<List<BaoGiaYeuCauSuaChuaChiTietListDto>> GetOverdueAsync(string donViId)
        {
            // Implementation will be added in next part
            return new List<BaoGiaYeuCauSuaChuaChiTietListDto>();
        }

        public async Task<List<BaoGiaYeuCauSuaChuaChiTietListDto>> GetPendingApprovalsAsync(string donViId)
        {
            // Implementation will be added in next part
            return new List<BaoGiaYeuCauSuaChuaChiTietListDto>();
        }

        public async Task<BaoGiaYeuCauSuaChuaChiTietStatisticsDto> GetStatisticsAsync(string donViId, string fromDate, string toDate)
        {
            // Implementation will be added in next part
            return new BaoGiaYeuCauSuaChuaChiTietStatisticsDto();
        }

        public async Task<bool> UpdateProgressAsync(string khoa, decimal phanTramHoanThanh, int trangThai, string nguoiCapNhat)
        {
            // Implementation will be added in next part
            return false;
        }

        public async Task<bool> AssignTechnicianAsync(string khoa, string khoaKTV, string nguoiCapNhat)
        {
            // Implementation will be added in next part
            return false;
        }

        public async Task<int> BulkUpdateStatusAsync(List<string> khoaList, int trangThai, string nguoiCapNhat)
        {
            // Implementation will be added in next part
            return 0;
        }

        public async Task<PaginatedResult<BaoGiaYeuCauSuaChuaChiTietListDto>> GetForMobileAsync(string donViId, int pageSize, int pageNumber, string? searchTerm = null)
        {
            // Implementation will be added in next part
            return new PaginatedResult<BaoGiaYeuCauSuaChuaChiTietListDto>();
        }

        public async Task<List<BaoGiaYeuCauSuaChuaChiTietListDto>> SearchAsync(BaoGiaYeuCauSuaChuaChiTietSearchDto searchDto)
        {
            // Implementation will be added in next part
            return new List<BaoGiaYeuCauSuaChuaChiTietListDto>();
        }

        public async Task<List<BaoGiaYeuCauSuaChuaChiTietLookupDto>> GetLookupDataAsync(string donViId)
        {
            // Implementation will be added in next part
            return new List<BaoGiaYeuCauSuaChuaChiTietLookupDto>();
        }

        public async Task<BaoGiaYeuCauSuaChuaChiTietValidationDto> ValidateAsync(string khoa)
        {
            // Implementation will be added in next part
            return new BaoGiaYeuCauSuaChuaChiTietValidationDto();
        }

        public async Task<List<AutomotiveRepairWorkDetailDto>> GetAutomotiveRepairWorkAsync(string khoaBaoGia)
        {
            // Implementation will be added in next part
            return new List<AutomotiveRepairWorkDetailDto>();
        }

        public async Task<RepairWorkSummaryDto> GetRepairWorkSummaryAsync(string khoaBaoGia)
        {
            // Implementation will be added in next part
            return new RepairWorkSummaryDto();
        }

        public async Task<List<TechnicianWorkloadDto>> GetTechnicianWorkloadAsync(string donViId, string fromDate, string toDate)
        {
            // Implementation will be added in next part
            return new List<TechnicianWorkloadDto>();
        }

        public async Task<WorkItemProgressDto?> GetWorkItemProgressAsync(string khoa)
        {
            // Implementation will be added in next part
            return null;
        }

        public async Task<bool> UpdateWorkItemProgressAsync(string khoa, WorkItemProgressDto progressDto)
        {
            // Implementation will be added in next part
            return false;
        }

        public async Task<bool> ExistsAsync(string khoa)
        {
            try
            {
                const string sql = "SELECT COUNT(1) FROM [SC_BaoGiaYeuCauSuaChuaChiTiet] WHERE Khoa = @Khoa";
                using var connection = new SqlConnection(_connectionString);
                var count = await connection.QuerySingleAsync<int>(sql, new { Khoa = khoa });
                return count > 0;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> CanDeleteAsync(string khoa)
        {
            try
            {
                // Check if repair requirement is in progress or completed
                const string sql = "SELECT TrangThai FROM [SC_BaoGiaYeuCauSuaChuaChiTiet] WHERE Khoa = @Khoa";
                using var connection = new SqlConnection(_connectionString);
                var trangThai = await connection.QuerySingleOrDefaultAsync<int?>(sql, new { Khoa = khoa });
                
                // Can only delete if pending (0) or cancelled (3)
                return trangThai == 0 || trangThai == 3;
            }
            catch
            {
                return false;
            }
        }

        public async Task<List<BaoGiaYeuCauSuaChuaChiTietListDto>> GetByDateRangeAsync(string donViId, string fromDate, string toDate)
        {
            // Implementation will be added in next part
            return new List<BaoGiaYeuCauSuaChuaChiTietListDto>();
        }
    }
}
