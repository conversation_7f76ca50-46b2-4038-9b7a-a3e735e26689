using GP.Mobile.Core.Services;
using GP.Mobile.Core.Repositories;
using GP.Mobile.Core.Interfaces;
using GP.Mobile.Data;
using GP.Mobile.Data.Repositories;
using Microsoft.EntityFrameworkCore;
using Serilog;
using Microsoft.Data.SqlClient;
using System.Data;

var builder = WebApplication.CreateBuilder(args);

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .WriteTo.Console()
    .WriteTo.File("logs/gp-mobile-api-.txt", rollingInterval: RollingInterval.Day)
    .CreateLogger();

builder.Host.UseSerilog();

// Add services to the container.
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Database connection - using same connection as legacy system
var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
builder.Services.AddScoped<IDbConnection>(provider => new SqlConnection(connectionString));

// Entity Framework (if needed for new tables)
builder.Services.AddDbContext<GPMobileDbContext>(options =>
    options.UseSqlServer(connectionString));

// AutoMapper
builder.Services.AddAutoMapper(typeof(Program));

// Repository pattern
builder.Services.AddScoped<IDoiTuongRepository, DoiTuongRepository>();
builder.Services.AddScoped<ICoHoiRepository, CoHoiRepository>();
builder.Services.AddScoped<IBaoGiaRepository, BaoGiaRepository>();
builder.Services.AddScoped<IBaoGiaChiTietRepository, BaoGiaChiTietRepository>();
builder.Services.AddScoped<IBaoGiaSuaChuaRepository, BaoGiaSuaChuaRepository>();
builder.Services.AddScoped<IBaoGiaSuaChuaChiTietRepository, BaoGiaSuaChuaChiTietRepository>();
builder.Services.AddScoped<INhapKhoRepository, NhapKhoRepository>();
builder.Services.AddScoped<IXuatKhoRepository, XuatKhoRepository>();
builder.Services.AddScoped<IDonViTinhRepository, DonViTinhRepository>();
builder.Services.AddScoped<ILoaiTienRepository, LoaiTienRepository>();
builder.Services.AddScoped<IDonViRepository, DonViRepository>();
builder.Services.AddScoped<ILoaiDichVuRepository, LoaiDichVuRepository>();
builder.Services.AddScoped<ILoaiXeRepository, LoaiXeRepository>();
builder.Services.AddScoped<IHangSanXuatRepository, HangSanXuatRepository>();
builder.Services.AddScoped<IBaoDuongRepository, BaoDuongRepository>();
builder.Services.AddScoped<IXeRepository, XeRepository>();
builder.Services.AddScoped<IKhoRepository, KhoRepository>();
builder.Services.AddScoped<IHangHoaRepository, HangHoaRepository>();
builder.Services.AddScoped<INhomHangHoaRepository, NhomHangHoaRepository>();
builder.Services.AddScoped<INhanVienRepository, NhanVienRepository>();
builder.Services.AddScoped<IKhoanMucChiPhiRepository, KhoanMucChiPhiRepository>();
builder.Services.AddScoped<IKhoanMucSuaChuaRepository, KhoanMucSuaChuaRepository>();
builder.Services.AddScoped<ISoChungTuRepository, SoChungTuRepository>();
builder.Services.AddScoped<ILogRepository, LogRepository>();
builder.Services.AddScoped<IBaoGiaYeuCauSuaChuaChiTietRepository, BaoGiaYeuCauSuaChuaChiTietRepository>();
builder.Services.AddScoped<CongLaoDongRepository>();

// Authentication repositories and services
builder.Services.AddScoped<ITempBaoGiaRepository, TempBaoGiaRepository>();
builder.Services.AddScoped<IBaoGiaHinhAnhBHRepository, BaoGiaHinhAnhBHRepository>();
builder.Services.AddScoped<IDieuKhoanBaoGiaRepository, DieuKhoanBaoGiaRepository>();
builder.Services.AddScoped<IAuthenticationService, AuthenticationService>();

// Business services
builder.Services.AddScoped<IDoiTuongService, DoiTuongService>();
builder.Services.AddScoped<ICoHoiService, CoHoiService>();
builder.Services.AddScoped<IBaoGiaService, BaoGiaService>();
builder.Services.AddScoped<IBaoGiaChiTietService, BaoGiaChiTietService>();
builder.Services.AddScoped<IBaoGiaSuaChuaService, BaoGiaSuaChuaService>();
builder.Services.AddScoped<IBaoGiaSuaChuaChiTietService, BaoGiaSuaChuaChiTietService>();
builder.Services.AddScoped<INhapKhoService, NhapKhoService>();
builder.Services.AddScoped<IXuatKhoService, XuatKhoService>();
builder.Services.AddScoped<IDonViTinhService, DonViTinhService>();
builder.Services.AddScoped<ILoaiTienService, LoaiTienService>();
builder.Services.AddScoped<IDonViService, DonViService>();
builder.Services.AddScoped<ILoaiDichVuService, LoaiDichVuService>();
builder.Services.AddScoped<ILoaiXeService, LoaiXeService>();
builder.Services.AddScoped<IHangSanXuatService, HangSanXuatService>();
builder.Services.AddScoped<IBaoDuongService, BaoDuongService>();
builder.Services.AddScoped<IXeService, XeService>();
builder.Services.AddScoped<IKhoService, KhoService>();
builder.Services.AddScoped<IHangHoaService, HangHoaService>();
builder.Services.AddScoped<INhomHangHoaService, NhomHangHoaService>();
builder.Services.AddScoped<INhanVienService, NhanVienService>();
builder.Services.AddScoped<IKhoanMucChiPhiService, KhoanMucChiPhiService>();
builder.Services.AddScoped<IKhoanMucSuaChuaService, KhoanMucSuaChuaService>();
builder.Services.AddScoped<ISoChungTuService, SoChungTuService>();
builder.Services.AddScoped<ILogService, LogService>();
builder.Services.AddScoped<IBaoGiaYeuCauSuaChuaChiTietService, BaoGiaYeuCauSuaChuaChiTietService>();
builder.Services.AddScoped<CongLaoDongService>();

// Authentication services
builder.Services.AddScoped<ITempBaoGiaService, TempBaoGiaService>();
builder.Services.AddScoped<IBaoGiaHinhAnhBHService, BaoGiaHinhAnhBHService>();
builder.Services.AddScoped<IDieuKhoanBaoGiaService, DieuKhoanBaoGiaService>();

// CORS for mobile app
builder.Services.AddCors(options =>
{
    options.AddPolicy("MobileApp", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseCors("MobileApp");
app.UseAuthorization();
app.MapControllers();

Log.Information("GP Mobile API starting up...");

app.Run();
