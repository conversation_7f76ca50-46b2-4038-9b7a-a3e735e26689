using System;
using System.Collections.Generic;

namespace GP.Mobile.Models.DTOs
{
    /// <summary>
    /// Paginated result for mobile app
    /// </summary>
    public class PaginatedResult<T>
    {
        public List<T> Items { get; set; } = new List<T>();
        public int TotalCount { get; set; } = 0;
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public int TotalPages { get; set; } = 0;
        public bool HasNextPage { get; set; } = false;
        public bool HasPreviousPage { get; set; } = false;
    }

    /// <summary>
    /// Insurance image statistics
    /// </summary>
    public class InsuranceImageStatisticsDto
    {
        public int TotalImages { get; set; } = 0;
        public int PendingApproval { get; set; } = 0;
        public int ApprovedImages { get; set; } = 0;
        public int RejectedImages { get; set; } = 0;
        public decimal TotalApprovedAmount { get; set; } = 0;
        public decimal AverageApprovalAmount { get; set; } = 0;
        public double ApprovalRate { get; set; } = 0;
        public List<InsuranceCompanyStatDto> StatsByInsuranceCompany { get; set; } = new List<InsuranceCompanyStatDto>();
    }

    /// <summary>
    /// Statistics by insurance company
    /// </summary>
    public class InsuranceCompanyStatDto
    {
        public string KhoaBaoHiem { get; set; } = string.Empty;
        public string TenBaoHiem { get; set; } = string.Empty;
        public int TotalImages { get; set; } = 0;
        public int ApprovedImages { get; set; } = 0;
        public decimal TotalApprovedAmount { get; set; } = 0;
        public double ApprovalRate { get; set; } = 0;
    }

    /// <summary>
    /// Terms usage statistics
    /// </summary>
    public class TermsUsageStatisticsDto
    {
        public string Khoa { get; set; } = string.Empty;
        public string Ma { get; set; } = string.Empty;
        public string TenViet { get; set; } = string.Empty;
        public int TotalUsage { get; set; } = 0;
        public int QuotationUsage { get; set; } = 0;
        public int RepairUsage { get; set; } = 0;
        public int SettlementUsage { get; set; } = 0;
        public string LastUsedDate { get; set; } = string.Empty;
        public double UsageRate { get; set; } = 0;
    }

    /// <summary>
    /// Service result wrapper
    /// </summary>
    public class ServiceResult<T>
    {
        public bool IsSuccess { get; set; }
        public T? Data { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<string> Errors { get; set; } = new List<string>();

        public static ServiceResult<T> SuccessResult(T data, string message = "")
        {
            return new ServiceResult<T>
            {
                IsSuccess = true,
                Data = data,
                Message = message
            };
        }

        public static ServiceResult<T> ErrorResult(string message, List<string>? errors = null)
        {
            return new ServiceResult<T>
            {
                IsSuccess = false,
                Message = message,
                Errors = errors ?? new List<string>()
            };
        }
    }

    /// <summary>
    /// Validation result
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public List<string> ErrorMessages { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();

        public ValidationResult()
        {
            // Keep both properties in sync for backward compatibility
            Errors = ErrorMessages;
        }

        public static ValidationResult Success()
        {
            return new ValidationResult { IsValid = true };
        }

        public static ValidationResult Error(string error)
        {
            var result = new ValidationResult
            {
                IsValid = false
            };
            result.Errors.Add(error);
            result.ErrorMessages.Add(error);
            return result;
        }

        public static ValidationResult Error(List<string> errors)
        {
            var result = new ValidationResult
            {
                IsValid = false
            };
            result.Errors.AddRange(errors);
            result.ErrorMessages.AddRange(errors);
            return result;
        }
    }
}
