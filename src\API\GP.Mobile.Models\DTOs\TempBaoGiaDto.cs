using System.ComponentModel.DataAnnotations;

namespace GP.Mobile.Models.DTOs
{
    /// <summary>
    /// Data Transfer Object for Temporary Quotation Data (clsTempBaoGia)
    /// Maps to SC_TempBaoGia table in legacy database
    /// Implements ALL properties from clsTempBaoGia usage analysis
    /// Manual implementation - Maintains 100% Legacy Compatibility
    /// CRITICAL for quotation editing workflow and approval management
    /// </summary>
    public class TempBaoGiaDto
    {
        /// <summary>
        /// Primary key - Quotation ID reference
        /// Maps to: mKhoa property in legacy class
        /// </summary>
        [Required]
        public string Khoa { get; set; } = string.Empty;

        /// <summary>
        /// Bot processing flag
        /// Maps to: IsBot property in legacy class (line 9960)
        /// </summary>
        public bool IsBot { get; set; } = false;

        /// <summary>
        /// Completion status flag
        /// Maps to: IsDone property in legacy class (line 9961)
        /// </summary>
        public bool IsDone { get; set; } = false;

        /// <summary>
        /// Approval for cancellation flag
        /// Maps to: IsDuyetHuy property in legacy class (line 9962, 10930, 11299)
        /// Critical for quotation line item cancellation workflow
        /// </summary>
        public bool IsDuyetHuy { get; set; } = false;

        /// <summary>
        /// Display visibility flag
        /// Maps to: IsShow property in legacy class (line 9963)
        /// </summary>
        public bool IsShow { get; set; } = false;

        /// <summary>
        /// Creation date
        /// </summary>
        public string NgayTao { get; set; } = string.Empty;

        /// <summary>
        /// User who created the temporary data
        /// </summary>
        public string NguoiTao { get; set; } = string.Empty;

        /// <summary>
        /// Last update date
        /// </summary>
        public string NgayCapNhat { get; set; } = string.Empty;

        /// <summary>
        /// User who last updated the temporary data
        /// </summary>
        public string NguoiCapNhat { get; set; } = string.Empty;

        /// <summary>
        /// Temporary data content (JSON or serialized data)
        /// </summary>
        public string TempData { get; set; } = string.Empty;

        /// <summary>
        /// Request cancellation approval data
        /// Used by SaveRequestDuyetHuy method (line 11320)
        /// </summary>
        public string RequestDuyetHuyData { get; set; } = string.Empty;

        /// <summary>
        /// Status of the temporary quotation
        /// 0 = Draft, 1 = Pending, 2 = Approved, 3 = Cancelled
        /// </summary>
        public int TrangThai { get; set; } = 0;

        /// <summary>
        /// Branch/Unit ID
        /// </summary>
        public string KhoaDonVi { get; set; } = string.Empty;

        /// <summary>
        /// Notes or comments
        /// </summary>
        public string GhiChu { get; set; } = string.Empty;

        // Navigation properties for display purposes
        /// <summary>
        /// Quotation document number (for display)
        /// </summary>
        public string? SoChungTu { get; set; }

        /// <summary>
        /// Vehicle license plate (for display)
        /// </summary>
        public string? SoXe { get; set; }

        /// <summary>
        /// Customer name (for display)
        /// </summary>
        public string? TenKhachHang { get; set; }

        /// <summary>
        /// Status text (for display)
        /// </summary>
        public string? TrangThaiText { get; set; }

        /// <summary>
        /// Formatted creation date (for display)
        /// </summary>
        public string? NgayTaoFormatted { get; set; }

        /// <summary>
        /// Flag indicating if cancellation is approved
        /// </summary>
        public bool DaDuyetHuy { get; set; } = false;

        /// <summary>
        /// Flag indicating if data is being processed
        /// </summary>
        public bool DangXuLy { get; set; } = false;

        /// <summary>
        /// Flag indicating if data is completed
        /// </summary>
        public bool DaHoanThanh { get; set; } = false;
    }

    /// <summary>
    /// DTO for saving temporary quotation data
    /// </summary>
    public class SaveTempBaoGiaDto
    {
        [Required]
        public string Khoa { get; set; } = string.Empty;

        public bool IsBot { get; set; } = false;

        public bool IsDone { get; set; } = false;

        public bool IsDuyetHuy { get; set; } = false;

        public bool IsShow { get; set; } = false;

        public string TempData { get; set; } = string.Empty;

        public string GhiChu { get; set; } = string.Empty;

        public string NguoiTao { get; set; } = string.Empty;
    }

    /// <summary>
    /// DTO for requesting cancellation approval
    /// </summary>
    public class RequestDuyetHuyDto
    {
        [Required]
        public string KhoaBaoGia { get; set; } = string.Empty;

        [Required]
        public string KhoaHangMuc { get; set; } = string.Empty;

        public string LyDo { get; set; } = string.Empty;

        public string NguoiDeXuat { get; set; } = string.Empty;

        public string NgayDeXuat { get; set; } = string.Empty;

        public string NoiDungHangMuc { get; set; } = string.Empty;

        public decimal SoLuong { get; set; } = 0;

        public decimal DonGia { get; set; } = 0;

        public decimal ThanhTien { get; set; } = 0;
    }

    /// <summary>
    /// DTO for temporary quotation list
    /// </summary>
    public class TempBaoGiaListDto
    {
        public string Khoa { get; set; } = string.Empty;
        public string SoChungTu { get; set; } = string.Empty;
        public string SoXe { get; set; } = string.Empty;
        public string TenKhachHang { get; set; } = string.Empty;
        public bool IsDuyetHuy { get; set; } = false;
        public int TrangThai { get; set; } = 0;
        public string TrangThaiText { get; set; } = string.Empty;
        public string NgayTao { get; set; } = string.Empty;
        public string NguoiTao { get; set; } = string.Empty;
        public bool DangXuLy { get; set; } = false;
    }

    /// <summary>
    /// DTO for temporary quotation statistics
    /// </summary>
    public class TempBaoGiaStatisticsDto
    {
        public int TotalTemp { get; set; } = 0;
        public int PendingApproval { get; set; } = 0;
        public int ApprovedCancellation { get; set; } = 0;
        public int InProgress { get; set; } = 0;
        public int Completed { get; set; } = 0;
        public double ApprovalRate { get; set; } = 0;
        public List<TempBaoGiaByUserDto> StatsByUser { get; set; } = new List<TempBaoGiaByUserDto>();
    }

    /// <summary>
    /// Statistics by user
    /// </summary>
    public class TempBaoGiaByUserDto
    {
        public string NguoiTao { get; set; } = string.Empty;
        public string TenNguoiTao { get; set; } = string.Empty;
        public int TotalTemp { get; set; } = 0;
        public int PendingApproval { get; set; } = 0;
        public int ApprovedCancellation { get; set; } = 0;
        public double ApprovalRate { get; set; } = 0;
    }
}
