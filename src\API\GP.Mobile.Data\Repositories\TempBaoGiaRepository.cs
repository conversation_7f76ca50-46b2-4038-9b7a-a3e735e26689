using System.Data;
using Dapper;
using GP.Mobile.Core.Interfaces;
using GP.Mobile.Models.DTOs;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace GP.Mobile.Data.Repositories
{
    /// <summary>
    /// Repository implementation for Temporary Quotation Data (clsTempBaoGia)
    /// Implements exact legacy functionality from clsTempBaoGia usage analysis
    /// Handles temporary data operations for quotation editing workflow
    /// </summary>
    public class TempBaoGiaRepository : ITempBaoGiaRepository
    {
        private readonly string _connectionString;
        private readonly ILogger<TempBaoGiaRepository> _logger;

        public TempBaoGiaRepository(IConfiguration configuration, ILogger<TempBaoGiaRepository> logger)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection") ?? throw new ArgumentNullException(nameof(configuration));
            _logger = logger;
        }

        /// <summary>
        /// Load temporary quotation data by quotation ID
        /// Exact implementation from legacy Load method (line 10929)
        /// </summary>
        public async Task<TempBaoGiaDto?> LoadAsync(string khoaBaoGia)
        {
            try
            {
                const string sql = "SELECT * FROM [SC_TempBaoGia] WHERE Khoa = @KhoaBaoGia";
                
                using var connection = new SqlConnection(_connectionString);
                var result = await connection.QueryFirstOrDefaultAsync<TempBaoGiaDto>(sql, new { KhoaBaoGia = khoaBaoGia });
                
                if (result != null)
                {
                    // Set computed properties
                    result.DaDuyetHuy = result.IsDuyetHuy;
                    result.DangXuLy = result.TrangThai == 1;
                    result.DaHoanThanh = result.IsDone;
                    
                    // Set display text
                    result.TrangThaiText = result.TrangThai switch
                    {
                        0 => "Nháp",
                        1 => "Đang xử lý",
                        2 => "Đã duyệt",
                        3 => "Đã hủy",
                        _ => "Không xác định"
                    };

                    // Format date
                    if (!string.IsNullOrEmpty(result.NgayTao) && result.NgayTao.Length == 8)
                    {
                        result.NgayTaoFormatted = $"{result.NgayTao.Substring(6, 2)}/{result.NgayTao.Substring(4, 2)}/{result.NgayTao.Substring(0, 4)}";
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading temporary quotation data for {KhoaBaoGia}", khoaBaoGia);
                return null;
            }
        }

        /// <summary>
        /// Save temporary quotation data using stored procedure
        /// Exact implementation from legacy Save method (line 9964)
        /// </summary>
        public async Task<bool> SaveAsync(TempBaoGiaDto tempBaoGia)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                
                var parameters = new DynamicParameters();
                parameters.Add("@Khoa", tempBaoGia.Khoa, DbType.String);
                parameters.Add("@IsBot", tempBaoGia.IsBot, DbType.Boolean);
                parameters.Add("@IsDone", tempBaoGia.IsDone, DbType.Boolean);
                parameters.Add("@IsDuyetHuy", tempBaoGia.IsDuyetHuy, DbType.Boolean);
                parameters.Add("@IsShow", tempBaoGia.IsShow, DbType.Boolean);
                parameters.Add("@NgayTao", tempBaoGia.NgayTao, DbType.String);
                parameters.Add("@NguoiTao", tempBaoGia.NguoiTao, DbType.String);
                parameters.Add("@NgayCapNhat", tempBaoGia.NgayCapNhat, DbType.String);
                parameters.Add("@NguoiCapNhat", tempBaoGia.NguoiCapNhat, DbType.String);
                parameters.Add("@TempData", tempBaoGia.TempData, DbType.String);
                parameters.Add("@RequestDuyetHuyData", tempBaoGia.RequestDuyetHuyData, DbType.String);
                parameters.Add("@TrangThai", tempBaoGia.TrangThai, DbType.Int32);
                parameters.Add("@KhoaDonVi", tempBaoGia.KhoaDonVi, DbType.String);
                parameters.Add("@GhiChu", tempBaoGia.GhiChu, DbType.String);

                await connection.ExecuteAsync("sp_SC_TempBaoGia", parameters, commandType: CommandType.StoredProcedure);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving temporary quotation data for {Khoa}", tempBaoGia.Khoa);
                return false;
            }
        }

        /// <summary>
        /// Save temporary quotation data with specific properties
        /// Exact implementation from legacy Save method usage (lines 9959-9964)
        /// </summary>
        public async Task<bool> SaveAsync(SaveTempBaoGiaDto saveDto)
        {
            try
            {
                var tempBaoGia = new TempBaoGiaDto
                {
                    Khoa = saveDto.Khoa,
                    IsBot = saveDto.IsBot,
                    IsDone = saveDto.IsDone,
                    IsDuyetHuy = saveDto.IsDuyetHuy,
                    IsShow = saveDto.IsShow,
                    TempData = saveDto.TempData,
                    GhiChu = saveDto.GhiChu,
                    NguoiTao = saveDto.NguoiTao,
                    NgayTao = DateTime.Now.ToString("yyyyMMdd"),
                    NgayCapNhat = DateTime.Now.ToString("yyyyMMdd"),
                    NguoiCapNhat = saveDto.NguoiTao,
                    TrangThai = 0 // Draft
                };

                return await SaveAsync(tempBaoGia);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving temporary quotation data for {Khoa}", saveDto.Khoa);
                return false;
            }
        }

        /// <summary>
        /// Save request for cancellation approval
        /// Exact implementation from legacy SaveRequestDuyetHuy method (line 11320)
        /// </summary>
        public async Task<bool> SaveRequestDuyetHuyAsync(RequestDuyetHuyDto requestDto)
        {
            try
            {
                // First, load existing temp data
                var existingTemp = await LoadAsync(requestDto.KhoaBaoGia);
                if (existingTemp == null)
                {
                    // Create new temp data if doesn't exist
                    existingTemp = new TempBaoGiaDto
                    {
                        Khoa = requestDto.KhoaBaoGia,
                        NgayTao = DateTime.Now.ToString("yyyyMMdd"),
                        NguoiTao = requestDto.NguoiDeXuat
                    };
                }

                // Update cancellation request data
                var requestData = System.Text.Json.JsonSerializer.Serialize(requestDto);
                existingTemp.RequestDuyetHuyData = requestData;
                existingTemp.IsDuyetHuy = false; // Pending approval
                existingTemp.TrangThai = 1; // Pending
                existingTemp.NgayCapNhat = DateTime.Now.ToString("yyyyMMdd");
                existingTemp.NguoiCapNhat = requestDto.NguoiDeXuat;

                return await SaveAsync(existingTemp);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving cancellation request for quotation {KhoaBaoGia}", requestDto.KhoaBaoGia);
                return false;
            }
        }

        /// <summary>
        /// Delete temporary quotation data by quotation ID
        /// </summary>
        public async Task<bool> DeleteAsync(string khoaBaoGia)
        {
            try
            {
                const string sql = "DELETE FROM [SC_TempBaoGia] WHERE Khoa = @KhoaBaoGia";
                
                using var connection = new SqlConnection(_connectionString);
                var rowsAffected = await connection.ExecuteAsync(sql, new { KhoaBaoGia = khoaBaoGia });
                
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting temporary quotation data for {KhoaBaoGia}", khoaBaoGia);
                return false;
            }
        }

        /// <summary>
        /// Check if temporary quotation data exists
        /// </summary>
        public async Task<bool> ExistsAsync(string khoaBaoGia)
        {
            try
            {
                const string sql = "SELECT COUNT(1) FROM [SC_TempBaoGia] WHERE Khoa = @KhoaBaoGia";
                using var connection = new SqlConnection(_connectionString);
                var count = await connection.QuerySingleAsync<int>(sql, new { KhoaBaoGia = khoaBaoGia });
                return count > 0;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Get temporary quotations requiring approval
        /// </summary>
        public async Task<List<TempBaoGiaListDto>> GetPendingApprovalsAsync(string donViId)
        {
            try
            {
                const string sql = @"
                    SELECT 
                        temp.Khoa,
                        bg.SoChungTu,
                        bg.SoXe,
                        bg.TenKhachHang,
                        temp.IsDuyetHuy,
                        temp.TrangThai,
                        temp.NgayTao,
                        temp.NguoiTao,
                        CASE 
                            WHEN temp.TrangThai = 0 THEN N'Nháp'
                            WHEN temp.TrangThai = 1 THEN N'Đang xử lý'
                            WHEN temp.TrangThai = 2 THEN N'Đã duyệt'
                            WHEN temp.TrangThai = 3 THEN N'Đã hủy'
                            ELSE N'Không xác định'
                        END as TrangThaiText,
                        CASE WHEN temp.TrangThai = 1 THEN 1 ELSE 0 END as DangXuLy
                    FROM [SC_TempBaoGia] temp
                    INNER JOIN [SC_BaoGia] bg ON temp.Khoa = bg.Khoa
                    WHERE temp.TrangThai = 1 
                    AND bg.KhoaDonVi = @DonViId
                    AND temp.RequestDuyetHuyData IS NOT NULL
                    AND temp.RequestDuyetHuyData != ''
                    ORDER BY temp.NgayTao DESC";
                
                using var connection = new SqlConnection(_connectionString);
                var result = await connection.QueryAsync<TempBaoGiaListDto>(sql, new { DonViId = donViId });
                
                return result.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting pending approvals for unit {DonViId}", donViId);
                return new List<TempBaoGiaListDto>();
            }
        }

        /// <summary>
        /// Get temporary quotations by status
        /// </summary>
        public async Task<List<TempBaoGiaListDto>> GetByStatusAsync(int trangThai, string donViId)
        {
            try
            {
                const string sql = @"
                    SELECT
                        temp.Khoa,
                        bg.SoChungTu,
                        bg.SoXe,
                        bg.TenKhachHang,
                        temp.IsDuyetHuy,
                        temp.TrangThai,
                        temp.NgayTao,
                        temp.NguoiTao,
                        CASE
                            WHEN temp.TrangThai = 0 THEN N'Nháp'
                            WHEN temp.TrangThai = 1 THEN N'Đang xử lý'
                            WHEN temp.TrangThai = 2 THEN N'Đã duyệt'
                            WHEN temp.TrangThai = 3 THEN N'Đã hủy'
                            ELSE N'Không xác định'
                        END as TrangThaiText,
                        CASE WHEN temp.TrangThai = 1 THEN 1 ELSE 0 END as DangXuLy
                    FROM [SC_TempBaoGia] temp
                    INNER JOIN [SC_BaoGia] bg ON temp.Khoa = bg.Khoa
                    WHERE temp.TrangThai = @TrangThai
                    AND bg.KhoaDonVi = @DonViId
                    ORDER BY temp.NgayTao DESC";

                using var connection = new SqlConnection(_connectionString);
                var result = await connection.QueryAsync<TempBaoGiaListDto>(sql, new { TrangThai = trangThai, DonViId = donViId });

                return result.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting temp quotations by status {TrangThai} for unit {DonViId}", trangThai, donViId);
                return new List<TempBaoGiaListDto>();
            }
        }

        /// <summary>
        /// Get temporary quotations by user
        /// </summary>
        public async Task<List<TempBaoGiaListDto>> GetByUserAsync(string nguoiTao, string fromDate, string toDate)
        {
            try
            {
                const string sql = @"
                    SELECT
                        temp.Khoa,
                        bg.SoChungTu,
                        bg.SoXe,
                        bg.TenKhachHang,
                        temp.IsDuyetHuy,
                        temp.TrangThai,
                        temp.NgayTao,
                        temp.NguoiTao,
                        CASE
                            WHEN temp.TrangThai = 0 THEN N'Nháp'
                            WHEN temp.TrangThai = 1 THEN N'Đang xử lý'
                            WHEN temp.TrangThai = 2 THEN N'Đã duyệt'
                            WHEN temp.TrangThai = 3 THEN N'Đã hủy'
                            ELSE N'Không xác định'
                        END as TrangThaiText,
                        CASE WHEN temp.TrangThai = 1 THEN 1 ELSE 0 END as DangXuLy
                    FROM [SC_TempBaoGia] temp
                    INNER JOIN [SC_BaoGia] bg ON temp.Khoa = bg.Khoa
                    WHERE temp.NguoiTao = @NguoiTao
                    AND temp.NgayTao BETWEEN @FromDate AND @ToDate
                    ORDER BY temp.NgayTao DESC";

                using var connection = new SqlConnection(_connectionString);
                var result = await connection.QueryAsync<TempBaoGiaListDto>(sql, new { NguoiTao = nguoiTao, FromDate = fromDate, ToDate = toDate });

                return result.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting temp quotations by user {NguoiTao}", nguoiTao);
                return new List<TempBaoGiaListDto>();
            }
        }

        /// <summary>
        /// Get temporary quotations requiring cancellation approval
        /// </summary>
        public async Task<List<TempBaoGiaListDto>> GetCancellationRequestsAsync(string donViId)
        {
            try
            {
                const string sql = @"
                    SELECT
                        temp.Khoa,
                        bg.SoChungTu,
                        bg.SoXe,
                        bg.TenKhachHang,
                        temp.IsDuyetHuy,
                        temp.TrangThai,
                        temp.NgayTao,
                        temp.NguoiTao,
                        CASE
                            WHEN temp.TrangThai = 0 THEN N'Nháp'
                            WHEN temp.TrangThai = 1 THEN N'Đang xử lý'
                            WHEN temp.TrangThai = 2 THEN N'Đã duyệt'
                            WHEN temp.TrangThai = 3 THEN N'Đã hủy'
                            ELSE N'Không xác định'
                        END as TrangThaiText,
                        CASE WHEN temp.TrangThai = 1 THEN 1 ELSE 0 END as DangXuLy
                    FROM [SC_TempBaoGia] temp
                    INNER JOIN [SC_BaoGia] bg ON temp.Khoa = bg.Khoa
                    WHERE bg.KhoaDonVi = @DonViId
                    AND temp.RequestDuyetHuyData IS NOT NULL
                    AND temp.RequestDuyetHuyData != ''
                    ORDER BY temp.NgayTao DESC";

                using var connection = new SqlConnection(_connectionString);
                var result = await connection.QueryAsync<TempBaoGiaListDto>(sql, new { DonViId = donViId });

                return result.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cancellation requests for unit {DonViId}", donViId);
                return new List<TempBaoGiaListDto>();
            }
        }

        public async Task<bool> UpdateCancellationApprovalAsync(string khoaBaoGia, bool isDuyetHuy, string nguoiDuyet)
        {
            try
            {
                const string sql = @"
                    UPDATE [SC_TempBaoGia] 
                    SET IsDuyetHuy = @IsDuyetHuy,
                        TrangThai = @TrangThai,
                        NgayCapNhat = @NgayCapNhat,
                        NguoiCapNhat = @NguoiDuyet
                    WHERE Khoa = @KhoaBaoGia";
                
                using var connection = new SqlConnection(_connectionString);
                var parameters = new
                {
                    KhoaBaoGia = khoaBaoGia,
                    IsDuyetHuy = isDuyetHuy,
                    TrangThai = isDuyetHuy ? 2 : 3, // 2 = Approved, 3 = Rejected
                    NgayCapNhat = DateTime.Now.ToString("yyyyMMdd"),
                    NguoiDuyet = nguoiDuyet
                };
                
                var rowsAffected = await connection.ExecuteAsync(sql, parameters);
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating cancellation approval for quotation {KhoaBaoGia}", khoaBaoGia);
                return false;
            }
        }

        /// <summary>
        /// Get temporary quotation statistics
        /// </summary>
        public async Task<TempBaoGiaStatisticsDto> GetStatisticsAsync(string donViId, string fromDate, string toDate)
        {
            try
            {
                const string sql = @"
                    SELECT
                        COUNT(*) as TotalRecords,
                        SUM(CASE WHEN temp.TrangThai = 0 THEN 1 ELSE 0 END) as DraftRecords,
                        SUM(CASE WHEN temp.TrangThai = 1 THEN 1 ELSE 0 END) as PendingRecords,
                        SUM(CASE WHEN temp.TrangThai = 2 THEN 1 ELSE 0 END) as ApprovedRecords,
                        SUM(CASE WHEN temp.TrangThai = 3 THEN 1 ELSE 0 END) as CancelledRecords,
                        SUM(CASE WHEN temp.RequestDuyetHuyData IS NOT NULL AND temp.RequestDuyetHuyData != '' THEN 1 ELSE 0 END) as CancellationRequests
                    FROM [SC_TempBaoGia] temp
                    INNER JOIN [SC_BaoGia] bg ON temp.Khoa = bg.Khoa
                    WHERE bg.KhoaDonVi = @DonViId
                    AND temp.NgayTao BETWEEN @FromDate AND @ToDate";

                using var connection = new SqlConnection(_connectionString);
                var result = await connection.QueryFirstOrDefaultAsync(sql, new { DonViId = donViId, FromDate = fromDate, ToDate = toDate });

                return new TempBaoGiaStatisticsDto
                {
                    TotalRecords = result?.TotalRecords ?? 0,
                    DraftRecords = result?.DraftRecords ?? 0,
                    PendingRecords = result?.PendingRecords ?? 0,
                    ApprovedRecords = result?.ApprovedRecords ?? 0,
                    CancelledRecords = result?.CancelledRecords ?? 0,
                    CancellationRequests = result?.CancellationRequests ?? 0
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting statistics for unit {DonViId}", donViId);
                return new TempBaoGiaStatisticsDto();
            }
        }

        /// <summary>
        /// Clear completed temporary data
        /// </summary>
        public async Task<int> ClearCompletedDataAsync(string donViId, int olderThanDays = 30)
        {
            try
            {
                var cutoffDate = DateTime.Now.AddDays(-olderThanDays).ToString("yyyyMMdd");

                const string sql = @"
                    DELETE temp FROM [SC_TempBaoGia] temp
                    INNER JOIN [SC_BaoGia] bg ON temp.Khoa = bg.Khoa
                    WHERE bg.KhoaDonVi = @DonViId
                    AND temp.TrangThai IN (2, 3)
                    AND temp.NgayTao < @CutoffDate";

                using var connection = new SqlConnection(_connectionString);
                var rowsAffected = await connection.ExecuteAsync(sql, new { DonViId = donViId, CutoffDate = cutoffDate });

                return rowsAffected;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing completed data for unit {DonViId}", donViId);
                return 0;
            }
        }

        /// <summary>
        /// Bulk update status for multiple temporary quotations
        /// </summary>
        public async Task<int> BulkUpdateStatusAsync(List<string> khoaBaoGiaList, int trangThai, string nguoiCapNhat)
        {
            try
            {
                if (!khoaBaoGiaList.Any()) return 0;

                var parameters = string.Join(",", khoaBaoGiaList.Select((_, i) => $"@khoa{i}"));
                var sql = $@"
                    UPDATE [SC_TempBaoGia]
                    SET TrangThai = @TrangThai,
                        NgayCapNhat = @NgayCapNhat,
                        NguoiCapNhat = @NguoiCapNhat
                    WHERE Khoa IN ({parameters})";

                using var connection = new SqlConnection(_connectionString);
                var dynamicParams = new DynamicParameters();
                dynamicParams.Add("@TrangThai", trangThai);
                dynamicParams.Add("@NgayCapNhat", DateTime.Now.ToString("yyyyMMdd"));
                dynamicParams.Add("@NguoiCapNhat", nguoiCapNhat);

                for (int i = 0; i < khoaBaoGiaList.Count; i++)
                {
                    dynamicParams.Add($"@khoa{i}", khoaBaoGiaList[i]);
                }

                var rowsAffected = await connection.ExecuteAsync(sql, dynamicParams);
                return rowsAffected;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error bulk updating status for {Count} records", khoaBaoGiaList.Count);
                return 0;
            }
        }

        /// <summary>
        /// Get temporary quotations for mobile app with pagination
        /// </summary>
        public async Task<PaginatedResult<TempBaoGiaListDto>> GetForMobileAsync(string donViId, int pageSize, int pageNumber, string? searchTerm = null)
        {
            try
            {
                var offset = (pageNumber - 1) * pageSize;
                var searchCondition = string.IsNullOrEmpty(searchTerm) ? "" :
                    "AND (bg.SoChungTu LIKE @SearchTerm OR bg.SoXe LIKE @SearchTerm OR bg.TenKhachHang LIKE @SearchTerm)";

                var countSql = $@"
                    SELECT COUNT(*)
                    FROM [SC_TempBaoGia] temp
                    INNER JOIN [SC_BaoGia] bg ON temp.Khoa = bg.Khoa
                    WHERE bg.KhoaDonVi = @DonViId {searchCondition}";

                var dataSql = $@"
                    SELECT
                        temp.Khoa,
                        bg.SoChungTu,
                        bg.SoXe,
                        bg.TenKhachHang,
                        temp.IsDuyetHuy,
                        temp.TrangThai,
                        temp.NgayTao,
                        temp.NguoiTao,
                        CASE
                            WHEN temp.TrangThai = 0 THEN N'Nháp'
                            WHEN temp.TrangThai = 1 THEN N'Đang xử lý'
                            WHEN temp.TrangThai = 2 THEN N'Đã duyệt'
                            WHEN temp.TrangThai = 3 THEN N'Đã hủy'
                            ELSE N'Không xác định'
                        END as TrangThaiText,
                        CASE WHEN temp.TrangThai = 1 THEN 1 ELSE 0 END as DangXuLy
                    FROM [SC_TempBaoGia] temp
                    INNER JOIN [SC_BaoGia] bg ON temp.Khoa = bg.Khoa
                    WHERE bg.KhoaDonVi = @DonViId {searchCondition}
                    ORDER BY temp.NgayTao DESC
                    OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY";

                using var connection = new SqlConnection(_connectionString);
                var searchParam = string.IsNullOrEmpty(searchTerm) ? null : $"%{searchTerm}%";

                var totalCount = await connection.QuerySingleAsync<int>(countSql, new { DonViId = donViId, SearchTerm = searchParam });
                var data = await connection.QueryAsync<TempBaoGiaListDto>(dataSql, new { DonViId = donViId, SearchTerm = searchParam, Offset = offset, PageSize = pageSize });

                return new PaginatedResult<TempBaoGiaListDto>
                {
                    Data = data.ToList(),
                    TotalCount = totalCount,
                    PageNumber = pageNumber,
                    PageSize = pageSize,
                    TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting paginated temp quotations for mobile");
                return new PaginatedResult<TempBaoGiaListDto>();
            }
        }

        public async Task<string?> GetTempDataAsync(string khoaBaoGia)
        {
            try
            {
                const string sql = "SELECT TempData FROM [SC_TempBaoGia] WHERE Khoa = @KhoaBaoGia";
                using var connection = new SqlConnection(_connectionString);
                return await connection.QuerySingleOrDefaultAsync<string>(sql, new { KhoaBaoGia = khoaBaoGia });
            }
            catch
            {
                return null;
            }
        }

        public async Task<bool> UpdateTempDataAsync(string khoaBaoGia, string tempData, string nguoiCapNhat)
        {
            try
            {
                const string sql = @"
                    UPDATE [SC_TempBaoGia] 
                    SET TempData = @TempData,
                        NgayCapNhat = @NgayCapNhat,
                        NguoiCapNhat = @NguoiCapNhat
                    WHERE Khoa = @KhoaBaoGia";
                
                using var connection = new SqlConnection(_connectionString);
                var parameters = new
                {
                    KhoaBaoGia = khoaBaoGia,
                    TempData = tempData,
                    NgayCapNhat = DateTime.Now.ToString("yyyyMMdd"),
                    NguoiCapNhat = nguoiCapNhat
                };
                
                var rowsAffected = await connection.ExecuteAsync(sql, parameters);
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating temp data for quotation {KhoaBaoGia}", khoaBaoGia);
                return false;
            }
        }

        public async Task<string?> GetCancellationRequestDataAsync(string khoaBaoGia)
        {
            try
            {
                const string sql = "SELECT RequestDuyetHuyData FROM [SC_TempBaoGia] WHERE Khoa = @KhoaBaoGia";
                using var connection = new SqlConnection(_connectionString);
                return await connection.QuerySingleOrDefaultAsync<string>(sql, new { KhoaBaoGia = khoaBaoGia });
            }
            catch
            {
                return null;
            }
        }

        public async Task<bool> HasPendingCancellationRequestAsync(string khoaBaoGia)
        {
            try
            {
                const string sql = @"
                    SELECT COUNT(1) FROM [SC_TempBaoGia] 
                    WHERE Khoa = @KhoaBaoGia 
                    AND TrangThai = 1 
                    AND RequestDuyetHuyData IS NOT NULL 
                    AND RequestDuyetHuyData != ''";
                
                using var connection = new SqlConnection(_connectionString);
                var count = await connection.QuerySingleAsync<int>(sql, new { KhoaBaoGia = khoaBaoGia });
                return count > 0;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Get temporary quotations by date range
        /// </summary>
        public async Task<List<TempBaoGiaListDto>> GetByDateRangeAsync(string donViId, string fromDate, string toDate)
        {
            try
            {
                const string sql = @"
                    SELECT
                        temp.Khoa,
                        bg.SoChungTu,
                        bg.SoXe,
                        bg.TenKhachHang,
                        temp.IsDuyetHuy,
                        temp.TrangThai,
                        temp.NgayTao,
                        temp.NguoiTao,
                        CASE
                            WHEN temp.TrangThai = 0 THEN N'Nháp'
                            WHEN temp.TrangThai = 1 THEN N'Đang xử lý'
                            WHEN temp.TrangThai = 2 THEN N'Đã duyệt'
                            WHEN temp.TrangThai = 3 THEN N'Đã hủy'
                            ELSE N'Không xác định'
                        END as TrangThaiText,
                        CASE WHEN temp.TrangThai = 1 THEN 1 ELSE 0 END as DangXuLy
                    FROM [SC_TempBaoGia] temp
                    INNER JOIN [SC_BaoGia] bg ON temp.Khoa = bg.Khoa
                    WHERE bg.KhoaDonVi = @DonViId
                    AND temp.NgayTao BETWEEN @FromDate AND @ToDate
                    ORDER BY temp.NgayTao DESC";

                using var connection = new SqlConnection(_connectionString);
                var result = await connection.QueryAsync<TempBaoGiaListDto>(sql, new { DonViId = donViId, FromDate = fromDate, ToDate = toDate });

                return result.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting temp quotations by date range for unit {DonViId}", donViId);
                return new List<TempBaoGiaListDto>();
            }
        }

        /// <summary>
        /// Archive old temporary data
        /// </summary>
        public async Task<int> ArchiveOldDataAsync(string donViId, int olderThanDays = 90)
        {
            try
            {
                var cutoffDate = DateTime.Now.AddDays(-olderThanDays).ToString("yyyyMMdd");

                // First, create archive table if it doesn't exist
                const string createArchiveSql = @"
                    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SC_TempBaoGia_Archive' AND xtype='U')
                    BEGIN
                        SELECT * INTO SC_TempBaoGia_Archive FROM SC_TempBaoGia WHERE 1=0
                        ALTER TABLE SC_TempBaoGia_Archive ADD ArchivedDate VARCHAR(8)
                    END";

                // Insert into archive
                const string archiveSql = @"
                    INSERT INTO SC_TempBaoGia_Archive
                    SELECT temp.*, @ArchivedDate as ArchivedDate
                    FROM [SC_TempBaoGia] temp
                    INNER JOIN [SC_BaoGia] bg ON temp.Khoa = bg.Khoa
                    WHERE bg.KhoaDonVi = @DonViId
                    AND temp.NgayTao < @CutoffDate";

                // Delete from main table
                const string deleteSql = @"
                    DELETE temp FROM [SC_TempBaoGia] temp
                    INNER JOIN [SC_BaoGia] bg ON temp.Khoa = bg.Khoa
                    WHERE bg.KhoaDonVi = @DonViId
                    AND temp.NgayTao < @CutoffDate";

                using var connection = new SqlConnection(_connectionString);
                using var transaction = connection.BeginTransaction();

                try
                {
                    // Create archive table
                    await connection.ExecuteAsync(createArchiveSql, transaction: transaction);

                    // Archive data
                    var archivedCount = await connection.ExecuteAsync(archiveSql, new
                    {
                        DonViId = donViId,
                        CutoffDate = cutoffDate,
                        ArchivedDate = DateTime.Now.ToString("yyyyMMdd")
                    }, transaction: transaction);

                    // Delete from main table
                    await connection.ExecuteAsync(deleteSql, new { DonViId = donViId, CutoffDate = cutoffDate }, transaction: transaction);

                    transaction.Commit();
                    return archivedCount;
                }
                catch
                {
                    transaction.Rollback();
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error archiving old data for unit {DonViId}", donViId);
                return 0;
            }
        }
    }
}
